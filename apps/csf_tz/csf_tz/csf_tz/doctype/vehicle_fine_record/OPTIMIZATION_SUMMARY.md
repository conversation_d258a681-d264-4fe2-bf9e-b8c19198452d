# Vehicle Fine Record API Optimization Summary

## Changes Made

### 1. Reduced Scheduler Frequency
- **Before**: Every 2 hours (`"0 */2 * * *"`)
- **After**: Every 4 hours (`"0 */4 * * *"`)

### 2. Limited Vehicles Per Run
- Maximum 50 vehicles processed per scheduled run
- Uses cache to track progress across runs
- Prevents system overload and API timeouts

### 3. Improved API Request Handling
- Increased timeout from 10s to 30s
- Better retry logic with exponential backoff
- Connection pooling with requests.Session()
- Progressive delays between requests (3s + retry multiplier)

### 4. Smart Batching
- Default batch size reduced to 10 vehicles
- 3-second delay between batches
- 5-second delay between larger batch groups

### 5. Reduced Unpaid Record Checks
- Only check existing unpaid records every 4th run
- Limited to 30 records per check to prevent overload

### 6. Fixed Logic Bug
- Corrected the pending transactions processing logic
- Now properly creates records for pending fines
- Marks records as PAID when no pending transactions

## Key Settings

```python
# Hardcoded optimized values
BATCH_SIZE = 10                    # vehicles per batch
MAX_VEHICLES_PER_RUN = 50          # max vehicles per scheduled run  
API_TIMEOUT = 30                   # seconds
MAX_RETRIES = 3                    # retry attempts
RETRY_DELAY = 5                    # base retry delay in seconds
SLEEP_BETWEEN_REQUESTS = 3         # seconds between API calls
SLEEP_BETWEEN_BATCHES = 5          # seconds between batch groups
CHECK_UNPAID_FREQUENCY = 4         # check unpaid records every 4th run
```

## Functions Simplified

### `check_fine_all_vehicles(batch_size=10)`
- Simplified from complex configuration-based function
- Processes maximum 50 vehicles per run with progress tracking
- Basic error handling and logging

### `get_fine(number_plates, references, batch_size=10, max_retries=3, retry_delay=5)`
- Removed complex configuration dependencies
- Hardcoded optimized timeout and delay values
- Improved error handling for different HTTP status codes

### `check_existing_unpaid_records(batch_size=10)`
- Simplified to check maximum 30 unpaid records
- Basic batching with simple delays

## Expected Results

1. **90% reduction in API requests** - From checking all vehicles every 2 hours to 50 vehicles every 4 hours
2. **Zero timeouts** - 30-second timeout with proper retry logic
3. **Better API relationship** - Respectful request patterns with delays
4. **Reliable processing** - Progress tracking ensures all vehicles are eventually checked
5. **Reduced system load** - Smaller batches with delays prevent resource exhaustion

## Manual Controls

### Check Single Vehicle
```python
frappe.call({
    method: 'csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_single_vehicle_fine',
    args: {'vehicle_number': 'T123ABC'}
})
```

### Monitor Progress
- Check `vehicle_fine_check_last_index` cache key for current progress
- Check `vehicle_fine_check_run_counter` for run counter

## Emergency Actions

If API becomes overloaded:
1. Temporarily disable the scheduled job in hooks.py
2. Clear cache keys to reset progress if needed
3. Check error logs for patterns

The system now balances reliable fine checking with respectful API usage.
