# -*- coding: utf-8 -*-
# Configuration for Vehicle Fine Record API calls

import frappe

class FineCheckConfig:
    """
    Configuration class for fine checking to prevent API overload and timeouts
    """
    
    @staticmethod
    def get_config():
        """
        Get configuration from site config or use defaults
        """
        config = frappe.conf.get("vehicle_fine_check", {})
        
        return {
            # API request settings
            "api_timeout": config.get("api_timeout", 30),  # seconds
            "max_retries": config.get("max_retries", 3),
            "retry_delay": config.get("retry_delay", 5),  # seconds
            
            # Batch processing settings
            "batch_size": config.get("batch_size", 10),  # vehicles per batch
            "max_vehicles_per_run": config.get("max_vehicles_per_run", 50),  # max vehicles per scheduled run
            "sleep_between_requests": config.get("sleep_between_requests", 3),  # seconds
            "sleep_between_batches": config.get("sleep_between_batches", 5),  # seconds
            
            # Rate limiting
            "enable_rate_limiting": config.get("enable_rate_limiting", True),
            "requests_per_minute": config.get("requests_per_minute", 20),
            
            # Monitoring and logging
            "log_progress_every": config.get("log_progress_every", 20),  # vehicles
            "enable_detailed_logging": config.get("enable_detailed_logging", False),
            
            # Feature flags
            "enable_fine_checking": config.get("enable_fine_checking", True),
            "check_unpaid_records_frequency": config.get("check_unpaid_records_frequency", 4),  # every Nth run
        }
    
    @staticmethod
    def update_config(new_config):
        """
        Update configuration in site config
        """
        frappe.only_for("System Manager")
        
        current_config = frappe.conf.get("vehicle_fine_check", {})
        current_config.update(new_config)
        
        # Update site config
        from frappe.installer import update_site_config
        update_site_config("vehicle_fine_check", current_config)
        
        return {"message": "Configuration updated successfully"}
    
    @staticmethod
    def get_api_status():
        """
        Get current API status and rate limiting info
        """
        cache_key_requests = "tms_api_requests_count"
        cache_key_last_request = "tms_api_last_request_time"
        
        current_requests = frappe.cache.get(cache_key_requests) or 0
        last_request_time = frappe.cache.get(cache_key_last_request)
        
        config = FineCheckConfig.get_config()
        
        return {
            "requests_in_current_minute": current_requests,
            "max_requests_per_minute": config["requests_per_minute"],
            "last_request_time": last_request_time,
            "rate_limiting_enabled": config["enable_rate_limiting"],
            "api_enabled": config["enable_fine_checking"]
        }


@frappe.whitelist()
def get_fine_check_config():
    """
    API endpoint to get current configuration
    """
    return FineCheckConfig.get_config()


@frappe.whitelist()
def update_fine_check_config(**kwargs):
    """
    API endpoint to update configuration
    """
    return FineCheckConfig.update_config(kwargs)


@frappe.whitelist()
def get_api_status():
    """
    API endpoint to get API status
    """
    return FineCheckConfig.get_api_status()


@frappe.whitelist()
def disable_fine_checking():
    """
    Emergency function to disable fine checking
    """
    frappe.only_for("System Manager")
    return FineCheckConfig.update_config({"enable_fine_checking": False})


@frappe.whitelist()
def enable_fine_checking():
    """
    Function to re-enable fine checking
    """
    frappe.only_for("System Manager")
    return FineCheckConfig.update_config({"enable_fine_checking": True})
