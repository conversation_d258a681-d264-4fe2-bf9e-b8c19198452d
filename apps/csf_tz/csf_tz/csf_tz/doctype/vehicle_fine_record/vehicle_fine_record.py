# -*- coding: utf-8 -*-
# Copyright (c) 2020, <PERSON><PERSON><PERSON>ch and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
from frappe.model.document import Document
import frappe
from frappe import _
import requests
from requests.exceptions import Timeout
from bs4 import BeautifulSoup
from csf_tz.custom_api import print_out
import re
import json
from time import sleep
from frappe.utils import create_batch
from .fine_check_config import FineCheckConfig


class VehicleFineRecord(Document):
    def validate(self):
        """
        Validate the vehicle number plate and get the vehicle name

        1. Check if the vehicle number plate is valid
        2. Get the vehicle name from the vehicle number plate
        3. If the vehicle name is not found, set the vehicle name as the vehicle number plate
        """
        try:
            if self.vehicle:
                vehicle_name = frappe.get_value(
                    "Vehicle", {"number_plate": self.vehicle}, "name"
                )
                if vehicle_name:
                    self.vehicle_doc = vehicle_name
                else:
                    self.vehicle_doc = self.vehicle
        except Exception as e:
            frappe.log_error(
                title=f"Error in VehicleFineRecord.validate",
                message=frappe.get_traceback(),
            )


def check_fine_all_vehicles(batch_size=None, max_vehicles_per_run=None):
    """
    Optimized function to check fines for all vehicles with rate limiting and smart batching.

    Args:
        batch_size (int): Number of vehicles to process in each batch (uses config default if None)
        max_vehicles_per_run (int): Maximum vehicles to process in a single run (uses config default if None)
    """
    try:
        # Get configuration
        config = FineCheckConfig.get_config()

        # Check if fine checking is enabled
        if not config["enable_fine_checking"]:
            frappe.logger().info("Vehicle fine checking is disabled in configuration")
            return

        # Use config defaults if not provided
        batch_size = batch_size or config["batch_size"]
        max_vehicles_per_run = max_vehicles_per_run or config["max_vehicles_per_run"]
        # Get all vehicles
        plate_list = frappe.get_all(
            "Vehicle",
            fields=["name", "number_plate"],
            limit_page_length=0
        )

        if not plate_list:
            frappe.logger().info("No vehicles found for fine checking")
            return

        all_number_plates = [v["number_plate"] or v["name"] for v in plate_list if v["number_plate"] or v["name"]]
        total_vehicles = len(all_number_plates)

        frappe.logger().info(f"Starting fine check for {total_vehicles} vehicles")

        # Limit the number of vehicles processed per run to prevent timeouts
        if total_vehicles > max_vehicles_per_run:
            # Get the last processed index from cache or start from 0
            cache_key = "vehicle_fine_check_last_index"
            last_index = frappe.cache.get(cache_key) or 0

            # Calculate end index for this run
            end_index = min(last_index + max_vehicles_per_run, total_vehicles)

            # Get the subset of vehicles to process
            vehicles_to_process = all_number_plates[last_index:end_index]

            frappe.logger().info(f"Processing vehicles {last_index} to {end_index} of {total_vehicles}")

            # Update the cache for next run
            next_index = end_index if end_index < total_vehicles else 0
            frappe.cache.set(cache_key, next_index, expires_in_sec=86400)  # 24 hours
        else:
            vehicles_to_process = all_number_plates

        # Process vehicles in smaller batches
        processed_count = 0
        for i in range(0, len(vehicles_to_process), batch_size):
            batch_plates = vehicles_to_process[i : i + batch_size]

            try:
                fine_list = get_fine(number_plates=batch_plates, batch_size=batch_size)
                processed_count += len(batch_plates)

                # Log progress
                frappe.logger().info(f"Processed batch {i//batch_size + 1}, vehicles: {processed_count}/{len(vehicles_to_process)}")

            except Exception as e:
                frappe.log_error(f"Error processing batch {i//batch_size + 1}: {str(e)}", "Vehicle Fine Batch Error")
                continue

        # Check existing unpaid records (less frequently)
        # Only do this every Nth run to reduce API load
        run_counter_key = "vehicle_fine_check_run_counter"
        run_counter = frappe.cache.get(run_counter_key) or 0
        run_counter += 1
        frappe.cache.set(run_counter_key, run_counter, expires_in_sec=86400)

        check_frequency = config["check_unpaid_records_frequency"]
        if run_counter % check_frequency == 0:  # Every Nth run based on config
            check_existing_unpaid_records(batch_size)

        frappe.logger().info(f"Completed fine check run. Processed {processed_count} vehicles.")

    except Exception as e:
        frappe.log_error(f"Error in check_fine_all_vehicles: {str(e)}", "Vehicle Fine Check Error")


def check_existing_unpaid_records(batch_size=10):
    """
    Check existing unpaid fine records to see if they've been paid
    """
    try:
        # Get unpaid records that haven't been checked recently
        unpaid_records = frappe.get_all(
            "Vehicle Fine Record",
            filters={
                "status": ["!=", "PAID"],
                "modified": ["<", frappe.utils.add_days(frappe.utils.now(), -1)]  # Not modified in last 24 hours
            },
            fields=["vehicle"],
            limit_page_length=50  # Limit to prevent overload
        )

        if not unpaid_records:
            return

        vehicles_to_check = [r["vehicle"] for r in unpaid_records]
        frappe.logger().info(f"Checking {len(vehicles_to_check)} vehicles with existing unpaid records")

        # Process in batches
        for i in range(0, len(vehicles_to_check), batch_size):
            batch_vehicles = vehicles_to_check[i : i + batch_size]
            try:
                get_fine(references=batch_vehicles, batch_size=batch_size)
            except Exception as e:
                frappe.log_error(f"Error checking existing unpaid records batch {i//batch_size + 1}: {str(e)}", "Unpaid Records Check Error")
                continue

    except Exception as e:
        frappe.log_error(f"Error in check_existing_unpaid_records: {str(e)}", "Unpaid Records Check Error")


@frappe.whitelist()
def get_fine(number_plates=None, references=None, batch_size=None, max_retries=None, retry_delay=None):
    """
    Enhanced get_fine: Accepts a list of number plates or references and processes them in batches using create_batch.
    Optimized to prevent timeouts and reduce API load.
    Args:
        number_plates (list): List of number plates.
        references (list): List of references.
        batch_size (int): Batch size for processing (uses config default if None).
        max_retries (int): Max retries for 429 errors (uses config default if None).
        retry_delay (int): Initial delay in seconds for retry (uses config default if None).
    Returns:
        list: List of fine results.
    """
    # Get configuration
    config = FineCheckConfig.get_config()

    # Check if fine checking is enabled
    if not config["enable_fine_checking"]:
        frappe.logger().info("Vehicle fine checking is disabled in configuration")
        return []

    # Use config defaults if not provided
    batch_size = batch_size or config["batch_size"]
    max_retries = max_retries or config["max_retries"]
    retry_delay = retry_delay or config["retry_delay"]
    if not number_plates and not references:
        print_out(
            _("Please provide either number plates or references (as lists)"),
            alert=True,
            add_traceback=True,
            to_error_log=True,
        )
        return []

    # Combine both lists if provided
    items = []
    if number_plates:
        items.extend(number_plates)
    if references:
        items.extend(references)

    # Check if we have too many items and warn
    if len(items) > 100:
        frappe.log_error(
            f"Processing {len(items)} vehicles - this may take a while and could impact API performance",
            "Large Vehicle Fine Check"
        )

    results = []
    url = "https://tms.tpf.go.tz/api/OffenceCheck"
    headers = {"Content-Type": "application/json", "Accept": "application/json"}

    # Add request session for connection pooling
    session = requests.Session()
    session.headers.update(headers)

    processed_count = 0
    total_items = len(items)

    try:
        for batch in create_batch(items, batch_size):
            for item in batch:
                payload = {"vehicle": item}
                retries = 0
                success = False

                while retries <= max_retries and not success:
                    try:
                        # Progressive delay based on config
                        sleep_time = config["sleep_between_requests"] + (retries * 2)
                        sleep(sleep_time)

                        # Use configured timeout
                        response = session.post(url, json=payload, timeout=config["api_timeout"])
                        response.raise_for_status()

                        try:
                            result = response.json()
                        except ValueError as e:
                            frappe.log_error(f"Invalid JSON response for vehicle {item}: {str(e)}", "TMS API JSON Error")
                            break

                        # Process the response
                        data = result.get("pending_transactions", [])

                        # Fixed logic: if there are pending transactions, process them
                        if data:
                            # Vehicle has pending fines - create or update records
                            for transaction in data:
                                create_or_update_fine_record(item, transaction)
                        else:
                            # No pending transactions - mark existing records as PAID if they exist
                            existing_records = frappe.get_all(
                                "Vehicle Fine Record",
                                filters={"vehicle": item, "status": ["!=", "PAID"]},
                                fields=["name"]
                            )
                            for record in existing_records:
                                doc = frappe.get_doc("Vehicle Fine Record", record.name)
                                doc.status = "PAID"
                                doc.save()

                        frappe.db.commit()
                        results.append(result)
                        success = True
                        processed_count += 1

                        # Log progress based on config
                        if processed_count % config["log_progress_every"] == 0:
                            frappe.logger().info(f"Processed {processed_count}/{total_items} vehicles for fine checking")

                    except requests.exceptions.HTTPError as e:
                        if hasattr(response, 'status_code'):
                            if response.status_code == 429:
                                # Rate limited - exponential backoff
                                backoff_time = retry_delay * (2 ** retries)
                                frappe.logger().warning(f"Rate limited for vehicle {item}, backing off for {backoff_time} seconds")
                                sleep(backoff_time)
                                retries += 1
                                continue
                            elif response.status_code >= 500:
                                # Server error - retry with backoff
                                frappe.log_error(f"Server error {response.status_code} for vehicle {item}: {str(e)}", "TMS API Server Error")
                                sleep(retry_delay * (retries + 1))
                                retries += 1
                                continue
                            else:
                                # Client error - don't retry
                                frappe.log_error(f"Client error {response.status_code} for vehicle {item}: {str(e)}", "TMS API Client Error")
                                break
                        else:
                            frappe.log_error(f"HTTP error for vehicle {item}: {str(e)}", "TMS API HTTP Error")
                            break

                    except requests.exceptions.Timeout as e:
                        frappe.log_error(f"Timeout error for vehicle {item}: {str(e)}", "TMS API Timeout")
                        retries += 1
                        if retries <= max_retries:
                            sleep(retry_delay * retries)
                            continue
                        else:
                            break

                    except requests.exceptions.ConnectionError as e:
                        frappe.log_error(f"Connection error for vehicle {item}: {str(e)}", "TMS API Connection Error")
                        retries += 1
                        if retries <= max_retries:
                            sleep(retry_delay * retries)
                            continue
                        else:
                            break

                    except Exception as e:
                        frappe.log_error(f"Unexpected error for vehicle {item}: {str(e)}", "TMS API Unexpected Error")
                        break

                if retries > max_retries and not success:
                    frappe.log_error(f"Max retries exceeded for vehicle {item}", "TMS API Max Retries")

            # Sleep between batches based on config
            if len(items) > batch_size:
                sleep(config["sleep_between_batches"])

    finally:
        session.close()

    frappe.logger().info(f"Completed fine checking for {processed_count}/{total_items} vehicles")
    return results


def create_or_update_fine_record(vehicle, transaction_data):
    """
    Create or update a Vehicle Fine Record based on transaction data
    """
    try:
        # Extract relevant data from transaction
        reference = transaction_data.get("reference") or transaction_data.get("id")
        amount = transaction_data.get("amount", 0)
        description = transaction_data.get("description", "")

        if not reference:
            frappe.log_error(f"No reference found in transaction data for vehicle {vehicle}", "Fine Record Creation Error")
            return

        # Check if record already exists
        existing_record = frappe.db.exists("Vehicle Fine Record", {"vehicle": vehicle, "reference": reference})

        if existing_record:
            # Update existing record
            doc = frappe.get_doc("Vehicle Fine Record", existing_record)
            doc.status = "PENDING"
            doc.amount = amount
            doc.description = description
            doc.save()
        else:
            # Create new record
            doc = frappe.new_doc("Vehicle Fine Record")
            doc.vehicle = vehicle
            doc.reference = reference
            doc.amount = amount
            doc.description = description
            doc.status = "PENDING"
            doc.insert()

    except Exception as e:
        frappe.log_error(f"Error creating/updating fine record for vehicle {vehicle}: {str(e)}", "Fine Record Error")


@frappe.whitelist()
def get_fine_check_status():
    """
    Get the current status of fine checking process
    """
    cache_key = "vehicle_fine_check_last_index"
    last_index = frappe.cache.get(cache_key) or 0

    run_counter_key = "vehicle_fine_check_run_counter"
    run_counter = frappe.cache.get(run_counter_key) or 0

    total_vehicles = frappe.db.count("Vehicle")

    return {
        "last_processed_index": last_index,
        "total_vehicles": total_vehicles,
        "run_counter": run_counter,
        "next_unpaid_check": 4 - (run_counter % 4),
        "progress_percentage": round((last_index / total_vehicles * 100), 2) if total_vehicles > 0 else 0
    }


@frappe.whitelist()
def reset_fine_check_progress():
    """
    Reset the fine checking progress - useful for manual intervention
    """
    frappe.only_for("System Manager")

    cache_key = "vehicle_fine_check_last_index"
    run_counter_key = "vehicle_fine_check_run_counter"

    frappe.cache.delete(cache_key)
    frappe.cache.delete(run_counter_key)

    return {"message": "Fine check progress has been reset"}


@frappe.whitelist()
def check_single_vehicle_fine(vehicle_number):
    """
    Check fine for a single vehicle - useful for testing and manual checks
    """
    if not vehicle_number:
        frappe.throw(_("Vehicle number is required"))

    try:
        result = get_fine(number_plates=[vehicle_number], batch_size=1)
        return {
            "success": True,
            "message": f"Fine check completed for vehicle {vehicle_number}",
            "result": result
        }
    except Exception as e:
        frappe.log_error(f"Error checking fine for vehicle {vehicle_number}: {str(e)}", "Single Vehicle Fine Check Error")
        return {
            "success": False,
            "message": f"Error checking fine for vehicle {vehicle_number}: {str(e)}"
        }