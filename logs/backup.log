set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250710_120002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-10 12:00:06.700144
Config  : ./mysite/private/backups/20250710_120002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250710_120002-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250710_180003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-10 18:00:07.339341
Config  : ./mysite/private/backups/20250710_180003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250710_180003-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250711_120002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-11 12:00:04.318513
Config  : ./mysite/private/backups/20250711_120002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250711_120002-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250711_180003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-11 18:00:10.872747
Config  : ./mysite/private/backups/20250711_180003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250711_180003-mysite-database.sql.gz         1.3MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250713_120002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-13 12:00:05.704913
Config  : ./mysite/private/backups/20250713_120002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250713_120002-mysite-database.sql.gz         1.3MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250713_180003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-13 18:00:08.975396
Config  : ./mysite/private/backups/20250713_180003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250713_180003-mysite-database.sql.gz         1.4MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250714_120002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-14 12:00:06.280038
Config  : ./mysite/private/backups/20250714_120002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250714_120002-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250714_180002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-14 18:00:05.452006
Config  : ./mysite/private/backups/20250714_180002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250714_180002-mysite-database.sql.gz         1.1MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250715_000002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-15 00:00:05.272616
Config  : ./mysite/private/backups/20250715_000002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250715_000002-mysite-database.sql.gz         1.1MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250715_120002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-15 12:00:06.476787
Config  : ./mysite/private/backups/20250715_120002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250715_120002-mysite-database.sql.gz         1.1MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_7574cff83173b5ba --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _7574cff83173b5ba | /usr/bin/gzip >> ./test-site/private/backups/20250715_120006-test-site-database.sql.gz

Backup Summary for test-site at 2025-07-15 12:00:10.313846
Config  : ./test-site/private/backups/20250715_120006-test-site-site_config_backup.json 94.0B
Database: ./test-site/private/backups/20250715_120006-test-site-database.sql.gz         847.1KiB
Backup for Site test-site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250715_180003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-15 18:00:08.201253
Config  : ./mysite/private/backups/20250715_180003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250715_180003-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
File ./test-site/private/backups/20250715_120006-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250715_120006-test-site-database.sql.gz is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_7574cff83173b5ba --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _7574cff83173b5ba | /usr/bin/gzip >> ./test-site/private/backups/20250715_180008-test-site-database.sql.gz

Backup Summary for test-site at 2025-07-15 18:00:11.499309
Config  : ./test-site/private/backups/20250715_180008-test-site-site_config_backup.json 94.0B
Database: ./test-site/private/backups/20250715_180008-test-site-database.sql.gz         855.8KiB
Backup for Site test-site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250716_000003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-16 00:00:08.251621
Config  : ./mysite/private/backups/20250716_000003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250716_000003-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
File ./test-site/private/backups/20250715_180008-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250715_120006-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250715_180008-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250715_120006-test-site-database.sql.gz is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_7574cff83173b5ba --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _7574cff83173b5ba | /usr/bin/gzip >> ./test-site/private/backups/20250716_000008-test-site-database.sql.gz

Backup Summary for test-site at 2025-07-16 00:00:11.580636
Config  : ./test-site/private/backups/20250716_000008-test-site-site_config_backup.json 94.0B
Database: ./test-site/private/backups/20250716_000008-test-site-database.sql.gz         856.4KiB
Backup for Site test-site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250716_060002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-16 06:00:06.045872
Config  : ./mysite/private/backups/20250716_060002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250716_060002-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
File ./test-site/private/backups/20250716_000008-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250715_180008-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250715_120006-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250715_180008-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250716_000008-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250715_120006-test-site-database.sql.gz is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_7574cff83173b5ba --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _7574cff83173b5ba | /usr/bin/gzip >> ./test-site/private/backups/20250716_060006-test-site-database.sql.gz

Backup Summary for test-site at 2025-07-16 06:00:08.629075
Config  : ./test-site/private/backups/20250716_060006-test-site-site_config_backup.json 94.0B
Database: ./test-site/private/backups/20250716_060006-test-site-database.sql.gz         856.4KiB
Backup for Site test-site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250716_120003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-16 12:00:08.821596
Config  : ./mysite/private/backups/20250716_120003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250716_120003-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
File ./test-site/private/backups/20250716_000008-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250715_180008-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250716_060006-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250715_120006-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250715_180008-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250716_060006-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250716_000008-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250715_120006-test-site-database.sql.gz is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_7574cff83173b5ba --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _7574cff83173b5ba | /usr/bin/gzip >> ./test-site/private/backups/20250716_120009-test-site-database.sql.gz

Backup Summary for test-site at 2025-07-16 12:00:12.588483
Config  : ./test-site/private/backups/20250716_120009-test-site-site_config_backup.json 94.0B
Database: ./test-site/private/backups/20250716_120009-test-site-database.sql.gz         856.4KiB
Backup for Site test-site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250716_180003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-16 18:00:07.905047
Config  : ./mysite/private/backups/20250716_180003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250716_180003-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
File ./test-site/private/backups/20250716_000008-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250715_180008-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250716_060006-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250716_120009-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250715_120006-test-site-site_config_backup.json is older than 24 hours
File ./test-site/private/backups/20250716_120009-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250715_180008-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250716_060006-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250716_000008-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250715_120006-test-site-database.sql.gz is older than 24 hours
set -o pipefail; /usr/bin/mariadb-dump --user=_7574cff83173b5ba --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _7574cff83173b5ba | /usr/bin/gzip >> ./test-site/private/backups/20250716_180008-test-site-database.sql.gz

Backup Summary for test-site at 2025-07-16 18:00:11.134758
Config  : ./test-site/private/backups/20250716_180008-test-site-site_config_backup.json 94.0B
Database: ./test-site/private/backups/20250716_180008-test-site-database.sql.gz         856.4KiB
Backup for Site test-site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250717_120001-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-17 12:00:04.369069
Config  : ./mysite/private/backups/20250717_120001-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250717_120001-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
File ./test-site/private/backups/20250716_000008-test-site-site_config_backup.json is older than 24 hours
File ./test-site/private/backups/20250715_180008-test-site-database.sql.gz is older than 24 hours
File ./test-site/private/backups/20250716_060006-test-site-database.sql.gz is older than 24 hours
File ./test-site/private/backups/20250716_120009-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250716_120009-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250716_180008-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250715_180008-test-site-site_config_backup.json is older than 24 hours
File ./test-site/private/backups/20250716_060006-test-site-site_config_backup.json is older than 24 hours
File ./test-site/private/backups/20250716_180008-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250716_000008-test-site-database.sql.gz is older than 24 hours
set -o pipefail; /usr/bin/mariadb-dump --user=_7574cff83173b5ba --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _7574cff83173b5ba | /usr/bin/gzip >> ./test-site/private/backups/20250717_120004-test-site-database.sql.gz

Backup Summary for test-site at 2025-07-17 12:00:06.043074
Config  : ./test-site/private/backups/20250717_120004-test-site-site_config_backup.json 94.0B
Database: ./test-site/private/backups/20250717_120004-test-site-database.sql.gz         856.4KiB
Backup for Site test-site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250717_180003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-17 18:00:09.780595
Config  : ./mysite/private/backups/20250717_180003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250717_180003-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
File ./test-site/private/backups/20250716_120009-test-site-database.sql.gz is older than 24 hours
File ./test-site/private/backups/20250716_120009-test-site-site_config_backup.json is older than 24 hours
File ./test-site/private/backups/20250716_180008-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250717_120004-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250716_180008-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250717_120004-test-site-database.sql.gz is recent
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x704b33f29080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x704b34982570>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x704b363f4f40>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x704b363f4f40>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x704b346fcef0>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x704b363f4f40>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x704b346fcef0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x704b363f5220>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x704b333697f0>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x704b3331d9e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x704b3331da80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x704b363f5220>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x704b363f5220>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x704b363f5220>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x704b363f5220>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x704b363f5220>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x704b3336b740>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x704b33e25580>, <class 'int'>: <function escape_int at 0x704b33e25620>, <class 'float'>: <function escape_float at 0x704b33e256c0>, <class 'str'>: <function escape_str at 0x704b33e25940>, <class 'bytes'>: <function escape_bytes at 0x704b33e258a0>, <class 'tuple'>: <function escape_sequence at 0x704b33e25440>, <class 'list'>: <function escape_sequence at 0x704b33e25440>, <class 'set'>: <function escape_sequence at 0x704b33e25440>, <class 'frozenset'>: <function escape_sequence at 0x704b33e25440>, <class 'dict'>: <function escape_dict at 0x704b33e253a0>, <class 'NoneType'>: <function escape_None at 0x704b33e259e0>, <class 'datetime.date'>: <function escape_date at 0x704b33e25c60>, <class 'datetime.datetime'>: <function escape_datetime at 0x704b33e25bc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x704b33e25a80>, <class 'datetime.time'>: <function escape_time at 0x704b33e25b20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x704b3336b740>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x704b3336b740>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x147\xb1\xee\xfb\xb3\xa1\x90\xa3\xd3\xad\xbe\xa4\xa8C \x86",\xbf\xed_7574cff83173b5ba\x00mysql_native_password\x006\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0529018'
      authresp = b'7\xb1\xee\xfb\xb3\xa1\x90\xa3\xd3\xad\xbe\xa4\xa8C \x86",\xbf\xed'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0529018'
      k = b'_pid'
      v = b'29018'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x704b3336b740>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x704b333d8e50>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x704b333d8e50>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250718_000003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-18 00:00:08.803470
Config  : ./mysite/private/backups/20250718_000003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250718_000003-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
File ./test-site/private/backups/20250716_180008-test-site-site_config_backup.json is older than 24 hours
File ./test-site/private/backups/20250717_120004-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250716_180008-test-site-database.sql.gz is older than 24 hours
File ./test-site/private/backups/20250717_120004-test-site-database.sql.gz is recent
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7e012a441080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x7e012a8de630>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7e012c38d180>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7e012c38d180>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7e012aecbc80>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7e012c38d180>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7e012aecbc80>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e012c38d460>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7e01292cb4a0>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7e012920d9e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7e012920da80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e012c38d460>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e012c38d460>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e012c38d460>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e012c38d460>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e012c38d460>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7e01292c9df0>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7e0129315580>, <class 'int'>: <function escape_int at 0x7e0129315620>, <class 'float'>: <function escape_float at 0x7e01293156c0>, <class 'str'>: <function escape_str at 0x7e0129315940>, <class 'bytes'>: <function escape_bytes at 0x7e01293158a0>, <class 'tuple'>: <function escape_sequence at 0x7e0129315440>, <class 'list'>: <function escape_sequence at 0x7e0129315440>, <class 'set'>: <function escape_sequence at 0x7e0129315440>, <class 'frozenset'>: <function escape_sequence at 0x7e0129315440>, <class 'dict'>: <function escape_dict at 0x7e01293153a0>, <class 'NoneType'>: <function escape_None at 0x7e01293159e0>, <class 'datetime.date'>: <function escape_date at 0x7e0129315c60>, <class 'datetime.datetime'>: <function escape_datetime at 0x7e0129315bc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7e0129315a80>, <class 'datetime.time'>: <function escape_time at 0x7e0129315b20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7e01292c9df0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7e01292c9df0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14q\xba\x025>)\xeb\xe4$\xca\x0c\xe5+D\xc2|\xf21\x93i_7574cff83173b5ba\x00mysql_native_password\x005\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x045083'
      authresp = b'q\xba\x025>)\xeb\xe4$\xca\x0c\xe5+D\xc2|\xf21\x93i'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x045083'
      k = b'_pid'
      v = b'5083'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7e01292c9df0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x7e01292c80d0>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7e01292c80d0>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250718_120002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-18 12:00:07.066064
Config  : ./mysite/private/backups/20250718_120002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250718_120002-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
File ./test-site/private/backups/20250717_120004-test-site-site_config_backup.json is older than 24 hours
File ./test-site/private/backups/20250717_120004-test-site-database.sql.gz is older than 24 hours
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x71313fc41080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x7131400de390>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x713141b8d300>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x713141b8d300>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x713141b8d250>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x713141b8d300>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x713141b8d250>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x71313fc4ce00>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x713141b8d010>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x71313ea0d9e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x71313ea0da80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x71313fc4ce00>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x71313fc4ce00>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x71313fc4ce00>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x71313fc4ce00>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x71313fc4ce00>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x713140377b00>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x71313eb15580>, <class 'int'>: <function escape_int at 0x71313eb15620>, <class 'float'>: <function escape_float at 0x71313eb156c0>, <class 'str'>: <function escape_str at 0x71313eb15940>, <class 'bytes'>: <function escape_bytes at 0x71313eb158a0>, <class 'tuple'>: <function escape_sequence at 0x71313eb15440>, <class 'list'>: <function escape_sequence at 0x71313eb15440>, <class 'set'>: <function escape_sequence at 0x71313eb15440>, <class 'frozenset'>: <function escape_sequence at 0x71313eb15440>, <class 'dict'>: <function escape_dict at 0x71313eb153a0>, <class 'NoneType'>: <function escape_None at 0x71313eb159e0>, <class 'datetime.date'>: <function escape_date at 0x71313eb15c60>, <class 'datetime.datetime'>: <function escape_datetime at 0x71313eb15bc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x71313eb15a80>, <class 'datetime.time'>: <function escape_time at 0x71313eb15b20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x713140377b00>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x713140377b00>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14J/az\x90\x9e\x0c\x96\xa9.~2\xcem:+\x9e1\x84\xfb_7574cff83173b5ba\x00mysql_native_password\x006\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0525427'
      authresp = b'J/az\x90\x9e\x0c\x96\xa9.~2\xcem:+\x9e1\x84\xfb'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0525427'
      k = b'_pid'
      v = b'25427'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x713140377b00>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x71313ea1f340>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x71313ea1f340>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250718_180003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-18 18:00:09.483064
Config  : ./mysite/private/backups/20250718_180003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250718_180003-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x79a53170d080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x79a5321d24b0>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x79a530bc2230>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x79a530bc2230>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x79a530bc2e70>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x79a530bc2230>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x79a530bc2e70>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x79a530b518b0>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x79a530bc3ad0>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x79a530b019e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x79a530b01a80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x79a530b518b0>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x79a530b518b0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x79a530b518b0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x79a530b518b0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x79a530b518b0>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x79a530bc1700>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x79a531609580>, <class 'int'>: <function escape_int at 0x79a531609620>, <class 'float'>: <function escape_float at 0x79a5316096c0>, <class 'str'>: <function escape_str at 0x79a531609940>, <class 'bytes'>: <function escape_bytes at 0x79a5316098a0>, <class 'tuple'>: <function escape_sequence at 0x79a531609440>, <class 'list'>: <function escape_sequence at 0x79a531609440>, <class 'set'>: <function escape_sequence at 0x79a531609440>, <class 'frozenset'>: <function escape_sequence at 0x79a531609440>, <class 'dict'>: <function escape_dict at 0x79a5316093a0>, <class 'NoneType'>: <function escape_None at 0x79a5316099e0>, <class 'datetime.date'>: <function escape_date at 0x79a531609c60>, <class 'datetime.datetime'>: <function escape_datetime at 0x79a531609bc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x79a531609a80>, <class 'datetime.time'>: <function escape_time at 0x79a531609b20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x79a530bc1700>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x79a530bc1700>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14+=\x16\xa0\xa48\xc8\xd4\xeb\n\xf0o\xec\xd0%\x1b"v\xd3u_7574cff83173b5ba\x00mysql_native_password\x005\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x045273'
      authresp = b'+=\x16\xa0\xa48\xc8\xd4\xeb\n\xf0o\xec\xd0%\x1b"v\xd3u'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x045273'
      k = b'_pid'
      v = b'5273'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x79a530bc1700>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x79a530bc37f0>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x79a530bc37f0>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250720_000003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-20 00:00:10.393833
Config  : ./mysite/private/backups/20250720_000003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250720_000003-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x70a104f11080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x70a104ef8770>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x70a10748cfd0>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x70a10748cfd0>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x70a10748cf50>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x70a10748cfd0>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x70a10748cf50>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x70a10571bf80>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x70a10748ce00>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x70a1043059e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x70a104305a80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x70a10571bf80>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x70a10571bf80>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x70a10571bf80>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x70a10571bf80>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x70a10571bf80>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x70a10579f620>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x70a104e0d580>, <class 'int'>: <function escape_int at 0x70a104e0d620>, <class 'float'>: <function escape_float at 0x70a104e0d6c0>, <class 'str'>: <function escape_str at 0x70a104e0d940>, <class 'bytes'>: <function escape_bytes at 0x70a104e0d8a0>, <class 'tuple'>: <function escape_sequence at 0x70a104e0d440>, <class 'list'>: <function escape_sequence at 0x70a104e0d440>, <class 'set'>: <function escape_sequence at 0x70a104e0d440>, <class 'frozenset'>: <function escape_sequence at 0x70a104e0d440>, <class 'dict'>: <function escape_dict at 0x70a104e0d3a0>, <class 'NoneType'>: <function escape_None at 0x70a104e0d9e0>, <class 'datetime.date'>: <function escape_date at 0x70a104e0dc60>, <class 'datetime.datetime'>: <function escape_datetime at 0x70a104e0dbc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x70a104e0da80>, <class 'datetime.time'>: <function escape_time at 0x70a104e0db20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x70a10579f620>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x70a10579f620>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14\x97u]/\x9e\xab\xcb\n\x86\xfa&\xb0\x14Y\xa11\xe7+\x102_7574cff83173b5ba\x00mysql_native_password\x006\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0561112'
      authresp = b'\x97u]/\x9e\xab\xcb\n\x86\xfa&\xb0\x14Y\xa11\xe7+\x102'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0561112'
      k = b'_pid'
      v = b'61112'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x70a10579f620>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x70a10748ce20>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x70a10748ce20>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250720_180002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-20 18:00:05.232023
Config  : ./mysite/private/backups/20250720_180002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250720_180002-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7804dac09080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x7804db0d2720>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7804dcb8d450>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7804dcb8d450>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7804dcb8d3d0>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7804dcb8d450>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7804dcb8d3d0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7804d9b33b00>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7804dcb8d280>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7804d9a0d9e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7804d9a0da80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7804d9b33b00>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7804d9b33b00>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7804d9b33b00>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7804d9b33b00>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7804d9b33b00>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7804dac4bc80>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7804d9b15580>, <class 'int'>: <function escape_int at 0x7804d9b15620>, <class 'float'>: <function escape_float at 0x7804d9b156c0>, <class 'str'>: <function escape_str at 0x7804d9b15940>, <class 'bytes'>: <function escape_bytes at 0x7804d9b158a0>, <class 'tuple'>: <function escape_sequence at 0x7804d9b15440>, <class 'list'>: <function escape_sequence at 0x7804d9b15440>, <class 'set'>: <function escape_sequence at 0x7804d9b15440>, <class 'frozenset'>: <function escape_sequence at 0x7804d9b15440>, <class 'dict'>: <function escape_dict at 0x7804d9b153a0>, <class 'NoneType'>: <function escape_None at 0x7804d9b159e0>, <class 'datetime.date'>: <function escape_date at 0x7804d9b15c60>, <class 'datetime.datetime'>: <function escape_datetime at 0x7804d9b15bc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7804d9b15a80>, <class 'datetime.time'>: <function escape_time at 0x7804d9b15b20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7804dac4bc80>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7804dac4bc80>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14f\x85\xe0;8\x9b\x8eTo\xab\xb35\x89\x11\x89\xdbo\x1a\xa3o_7574cff83173b5ba\x00mysql_native_password\x006\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0511497'
      authresp = b'f\x85\xe0;8\x9b\x8eTo\xab\xb35\x89\x11\x89\xdbo\x1a\xa3o'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0511497'
      k = b'_pid'
      v = b'11497'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7804dac4bc80>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x7804dcb8d2a0>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7804dcb8d2a0>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250721_000002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-21 00:00:03.694447
Config  : ./mysite/private/backups/20250721_000002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250721_000002-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7e732dce9080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x7e732df863c0>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7e732f9e8f40>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7e732f9e8f40>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7e732f9e8ec0>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7e732f9e8f40>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7e732f9e8ec0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e732dc06c00>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7e732f9e8d70>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7e732d4719e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7e732d471a80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e732dc06c00>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e732dc06c00>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e732dc06c00>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e732dc06c00>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e732dc06c00>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7e732e2595b0>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7e732d5f9580>, <class 'int'>: <function escape_int at 0x7e732d5f9620>, <class 'float'>: <function escape_float at 0x7e732d5f96c0>, <class 'str'>: <function escape_str at 0x7e732d5f9940>, <class 'bytes'>: <function escape_bytes at 0x7e732d5f98a0>, <class 'tuple'>: <function escape_sequence at 0x7e732d5f9440>, <class 'list'>: <function escape_sequence at 0x7e732d5f9440>, <class 'set'>: <function escape_sequence at 0x7e732d5f9440>, <class 'frozenset'>: <function escape_sequence at 0x7e732d5f9440>, <class 'dict'>: <function escape_dict at 0x7e732d5f93a0>, <class 'NoneType'>: <function escape_None at 0x7e732d5f99e0>, <class 'datetime.date'>: <function escape_date at 0x7e732d5f9c60>, <class 'datetime.datetime'>: <function escape_datetime at 0x7e732d5f9bc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7e732d5f9a80>, <class 'datetime.time'>: <function escape_time at 0x7e732d5f9b20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7e732e2595b0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7e732e2595b0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14\xce\xfb\xb6\xaf\x12\xae\xc8m\x82k\xedo\xcaP\x1f\xcf\x0c\x10\x1c\x03_7574cff83173b5ba\x00mysql_native_password\x007\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06578646'
      authresp = b'\xce\xfb\xb6\xaf\x12\xae\xc8m\x82k\xedo\xcaP\x1f\xcf\x0c\x10\x1c\x03'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06578646'
      k = b'_pid'
      v = b'578646'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7e732e2595b0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x7e732c941b10>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7e732c941b10>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250721_060001-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-21 06:00:03.267042
Config  : ./mysite/private/backups/20250721_060001-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250721_060001-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x72e291325080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x72e2915c2240>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x72e28ff40ac0>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x72e28ff40ac0>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x72e28ff417c0>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x72e28ff40ac0>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x72e28ff417c0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x72e292fe8e00>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x72e28ff9ea20>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x72e290af19e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x72e290af1a80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x72e292fe8e00>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x72e292fe8e00>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x72e292fe8e00>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x72e292fe8e00>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x72e292fe8e00>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x72e28ff9e0f0>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x72e290bf9580>, <class 'int'>: <function escape_int at 0x72e290bf9620>, <class 'float'>: <function escape_float at 0x72e290bf96c0>, <class 'str'>: <function escape_str at 0x72e290bf9940>, <class 'bytes'>: <function escape_bytes at 0x72e290bf98a0>, <class 'tuple'>: <function escape_sequence at 0x72e290bf9440>, <class 'list'>: <function escape_sequence at 0x72e290bf9440>, <class 'set'>: <function escape_sequence at 0x72e290bf9440>, <class 'frozenset'>: <function escape_sequence at 0x72e290bf9440>, <class 'dict'>: <function escape_dict at 0x72e290bf93a0>, <class 'NoneType'>: <function escape_None at 0x72e290bf99e0>, <class 'datetime.date'>: <function escape_date at 0x72e290bf9c60>, <class 'datetime.datetime'>: <function escape_datetime at 0x72e290bf9bc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x72e290bf9a80>, <class 'datetime.time'>: <function escape_time at 0x72e290bf9b20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x72e28ff9e0f0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x72e28ff9e0f0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14\xd0\x07\x89\x9d\x19u\xc5P\x89ImFi\xc9\xe5w\x94*\xba\xe6_7574cff83173b5ba\x00mysql_native_password\x007\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06579834'
      authresp = b'\xd0\x07\x89\x9d\x19u\xc5P\x89ImFi\xc9\xe5w\x94*\xba\xe6'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06579834'
      k = b'_pid'
      v = b'579834'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x72e28ff9e0f0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x72e28ff9eef0>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x72e28ff9eef0>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250721_120002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-21 12:00:06.345634
Config  : ./mysite/private/backups/20250721_120002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250721_120002-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7f403ba41080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x7f403bede570>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f403d98d360>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f403d98d360>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7f403d98d2b0>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f403d98d360>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7f403d98d2b0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f403ba4ca10>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7f403d98d1f0>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7f403a80d9e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7f403a80da80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f403ba4ca10>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f403ba4ca10>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f403ba4ca10>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f403ba4ca10>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f403ba4ca10>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7f403bf67b00>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7f403a915580>, <class 'int'>: <function escape_int at 0x7f403a915620>, <class 'float'>: <function escape_float at 0x7f403a9156c0>, <class 'str'>: <function escape_str at 0x7f403a915940>, <class 'bytes'>: <function escape_bytes at 0x7f403a9158a0>, <class 'tuple'>: <function escape_sequence at 0x7f403a915440>, <class 'list'>: <function escape_sequence at 0x7f403a915440>, <class 'set'>: <function escape_sequence at 0x7f403a915440>, <class 'frozenset'>: <function escape_sequence at 0x7f403a915440>, <class 'dict'>: <function escape_dict at 0x7f403a9153a0>, <class 'NoneType'>: <function escape_None at 0x7f403a9159e0>, <class 'datetime.date'>: <function escape_date at 0x7f403a915c60>, <class 'datetime.datetime'>: <function escape_datetime at 0x7f403a915bc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7f403a915a80>, <class 'datetime.time'>: <function escape_time at 0x7f403a915b20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7f403bf67b00>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7f403bf67b00>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14\xe8":o[sv\x9biK5\x7f\xde2\x83\xf4\xe2\n\x15B_7574cff83173b5ba\x00mysql_native_password\x006\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0518050'
      authresp = b'\xe8":o[sv\x9biK5\x7f\xde2\x83\xf4\xe2\n\x15B'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0518050'
      k = b'_pid'
      v = b'18050'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7f403bf67b00>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x7f403c4cbc70>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7f403c4cbc70>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250721_180002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-21 18:00:06.415874
Config  : ./mysite/private/backups/20250721_180002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250721_180002-mysite-database.sql.gz         1.5MiB
Backup for Site mysite has been successfully completed
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x749b6150d080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x749b61fd25a0>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x749b63a88fd0>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x749b63a88fd0>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x749b63a88f20>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x749b63a88fd0>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x749b63a88f20>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x749b61d963c0>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x749b63a88e60>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x749b609019e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x749b60901a80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x749b61d963c0>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x749b61d963c0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x749b61d963c0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x749b61d963c0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x749b61d963c0>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x749b627fd9a0>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x749b61409580>, <class 'int'>: <function escape_int at 0x749b61409620>, <class 'float'>: <function escape_float at 0x749b614096c0>, <class 'str'>: <function escape_str at 0x749b61409940>, <class 'bytes'>: <function escape_bytes at 0x749b614098a0>, <class 'tuple'>: <function escape_sequence at 0x749b61409440>, <class 'list'>: <function escape_sequence at 0x749b61409440>, <class 'set'>: <function escape_sequence at 0x749b61409440>, <class 'frozenset'>: <function escape_sequence at 0x749b61409440>, <class 'dict'>: <function escape_dict at 0x749b614093a0>, <class 'NoneType'>: <function escape_None at 0x749b614099e0>, <class 'datetime.date'>: <function escape_date at 0x749b61409c60>, <class 'datetime.datetime'>: <function escape_datetime at 0x749b61409bc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x749b61409a80>, <class 'datetime.time'>: <function escape_time at 0x749b61409b20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x749b627fd9a0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x749b627fd9a0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14C@\xb64\xe0I\x95Lu\xab\x15\x16%\xedp\x81\xecy\xb2\xd0_7574cff83173b5ba\x00mysql_native_password\x006\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0535656'
      authresp = b'C@\xb64\xe0I\x95Lu\xab\x15\x16%\xedp\x81\xecy\xb2\xd0'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0535656'
      k = b'_pid'
      v = b'35656'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x749b627fd9a0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x749b60916470>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x749b60916470>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250722_120002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-22 12:00:06.349182
Config  : ./mysite/private/backups/20250722_120002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250722_120002-mysite-database.sql.gz         1.5MiB
Backup for Site mysite has been successfully completed
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7c4100805080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x7c4100cca5a0>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c4102759120>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c4102759120>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7c41027590a0>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c4102759120>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7c41027590a0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7c4102759400>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7c40ff6261e0>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7c40ff6159e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7c40ff615a80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7c4102759400>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7c4102759400>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7c4102759400>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7c4102759400>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7c4102759400>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7c40ff624140>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7c40ff71d580>, <class 'int'>: <function escape_int at 0x7c40ff71d620>, <class 'float'>: <function escape_float at 0x7c40ff71d6c0>, <class 'str'>: <function escape_str at 0x7c40ff71d940>, <class 'bytes'>: <function escape_bytes at 0x7c40ff71d8a0>, <class 'tuple'>: <function escape_sequence at 0x7c40ff71d440>, <class 'list'>: <function escape_sequence at 0x7c40ff71d440>, <class 'set'>: <function escape_sequence at 0x7c40ff71d440>, <class 'frozenset'>: <function escape_sequence at 0x7c40ff71d440>, <class 'dict'>: <function escape_dict at 0x7c40ff71d3a0>, <class 'NoneType'>: <function escape_None at 0x7c40ff71d9e0>, <class 'datetime.date'>: <function escape_date at 0x7c40ff71dc60>, <class 'datetime.datetime'>: <function escape_datetime at 0x7c40ff71dbc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7c40ff71da80>, <class 'datetime.time'>: <function escape_time at 0x7c40ff71db20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7c40ff624140>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7c40ff624140>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14\xe3|\xa1k\xabzo\xc1\x84\x04P\x8d\xa1n\x06M~\xd6\x8d\xba_7574cff83173b5ba\x00mysql_native_password\x006\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0512932'
      authresp = b'\xe3|\xa1k\xabzo\xc1\x84\x04P\x8d\xa1n\x06M~\xd6\x8d\xba'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0512932'
      k = b'_pid'
      v = b'12932'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7c40ff624140>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x7c40ff6d1420>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7c40ff6d1420>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250722_180002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-22 18:00:05.215986
Config  : ./mysite/private/backups/20250722_180002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250722_180002-mysite-database.sql.gz         1.5MiB
Backup for Site mysite has been successfully completed
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7ecfcedfd080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x7ecfcf2ce4e0>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7ecfcdc12320>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7ecfcdc12320>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7ecfcdc12210>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7ecfcdc12320>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7ecfcdc12210>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7ecfcee715b0>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7ecfcdc12900>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7ecfcdc019e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7ecfcdc01a80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7ecfcee715b0>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7ecfcee715b0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7ecfcee715b0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7ecfcee715b0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7ecfcee715b0>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7ecfcdc121e0>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7ecfcdd09580>, <class 'int'>: <function escape_int at 0x7ecfcdd09620>, <class 'float'>: <function escape_float at 0x7ecfcdd096c0>, <class 'str'>: <function escape_str at 0x7ecfcdd09940>, <class 'bytes'>: <function escape_bytes at 0x7ecfcdd098a0>, <class 'tuple'>: <function escape_sequence at 0x7ecfcdd09440>, <class 'list'>: <function escape_sequence at 0x7ecfcdd09440>, <class 'set'>: <function escape_sequence at 0x7ecfcdd09440>, <class 'frozenset'>: <function escape_sequence at 0x7ecfcdd09440>, <class 'dict'>: <function escape_dict at 0x7ecfcdd093a0>, <class 'NoneType'>: <function escape_None at 0x7ecfcdd099e0>, <class 'datetime.date'>: <function escape_date at 0x7ecfcdd09c60>, <class 'datetime.datetime'>: <function escape_datetime at 0x7ecfcdd09bc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7ecfcdd09a80>, <class 'datetime.time'>: <function escape_time at 0x7ecfcdd09b20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7ecfcdc121e0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7ecfcdc121e0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b"\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14d\xef\xbev\xaf\xed\xe0e\xb3\x8ez\xe5JZ\x16w\x1d'\xac\xf8_7574cff83173b5ba\x00mysql_native_password\x006\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0520454"
      authresp = b"d\xef\xbev\xaf\xed\xe0e\xb3\x8ez\xe5JZ\x16w\x1d'\xac\xf8"
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0520454'
      k = b'_pid'
      v = b'20454'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7ecfcdc121e0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x7ecfcddf7730>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7ecfcddf7730>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250723_000002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-23 00:00:03.980285
Config  : ./mysite/private/backups/20250723_000002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250723_000002-mysite-database.sql.gz         1.5MiB
Backup for Site mysite has been successfully completed
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x731e25205080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x731e256d64b0>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x731e27189240>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x731e27189240>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x731e271891c0>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x731e27189240>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x731e271891c0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x731e24016570>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x731e27189070>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x731e240099e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x731e24009a80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x731e24016570>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x731e24016570>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x731e24016570>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x731e24016570>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x731e24016570>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x731e25491ca0>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x731e24111580>, <class 'int'>: <function escape_int at 0x731e24111620>, <class 'float'>: <function escape_float at 0x731e241116c0>, <class 'str'>: <function escape_str at 0x731e24111940>, <class 'bytes'>: <function escape_bytes at 0x731e241118a0>, <class 'tuple'>: <function escape_sequence at 0x731e24111440>, <class 'list'>: <function escape_sequence at 0x731e24111440>, <class 'set'>: <function escape_sequence at 0x731e24111440>, <class 'frozenset'>: <function escape_sequence at 0x731e24111440>, <class 'dict'>: <function escape_dict at 0x731e241113a0>, <class 'NoneType'>: <function escape_None at 0x731e241119e0>, <class 'datetime.date'>: <function escape_date at 0x731e24111c60>, <class 'datetime.datetime'>: <function escape_datetime at 0x731e24111bc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x731e24111a80>, <class 'datetime.time'>: <function escape_time at 0x731e24111b20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x731e25491ca0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x731e25491ca0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14;5Q\xd8c\xd6\xd94\x8cVJ\xdb\xab\xd7\xc4<\xd0\xd5\x97\xe5_7574cff83173b5ba\x00mysql_native_password\x006\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0542797'
      authresp = b';5Q\xd8c\xd6\xd94\x8cVJ\xdb\xab\xd7\xc4<\xd0\xd5\x97\xe5'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0542797'
      k = b'_pid'
      v = b'42797'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x731e25491ca0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x731e24051120>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x731e24051120>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250723_120003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-23 12:00:10.643173
Config  : ./mysite/private/backups/20250723_120003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250723_120003-mysite-database.sql.gz         1.5MiB
Backup for Site mysite has been successfully completed
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7e1ab37d5080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x7e1ab3ca6330>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7e1ab26d5bd0>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7e1ab26d5bd0>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7e1ab26d65a0>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7e1ab26d5bd0>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7e1ab26d65a0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e1ab574d280>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7e1ab26d6c30>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7e1ab26159e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7e1ab2615a80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e1ab574d280>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e1ab574d280>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e1ab574d280>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e1ab574d280>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e1ab574d280>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7e1ab26d78f0>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7e1ab271d580>, <class 'int'>: <function escape_int at 0x7e1ab271d620>, <class 'float'>: <function escape_float at 0x7e1ab271d6c0>, <class 'str'>: <function escape_str at 0x7e1ab271d940>, <class 'bytes'>: <function escape_bytes at 0x7e1ab271d8a0>, <class 'tuple'>: <function escape_sequence at 0x7e1ab271d440>, <class 'list'>: <function escape_sequence at 0x7e1ab271d440>, <class 'set'>: <function escape_sequence at 0x7e1ab271d440>, <class 'frozenset'>: <function escape_sequence at 0x7e1ab271d440>, <class 'dict'>: <function escape_dict at 0x7e1ab271d3a0>, <class 'NoneType'>: <function escape_None at 0x7e1ab271d9e0>, <class 'datetime.date'>: <function escape_date at 0x7e1ab271dc60>, <class 'datetime.datetime'>: <function escape_datetime at 0x7e1ab271dbc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7e1ab271da80>, <class 'datetime.time'>: <function escape_time at 0x7e1ab271db20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7e1ab26d78f0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7e1ab26d78f0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14\xc2K\xeb\xcd\x18$\x7f\x02\xd7|\xda\xb3\xe1>\x0b?\xdd\xcd\xc7J_7574cff83173b5ba\x00mysql_native_password\x006\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0515080'
      authresp = b'\xc2K\xeb\xcd\x18$\x7f\x02\xd7|\xda\xb3\xe1>\x0b?\xdd\xcd\xc7J'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0515080'
      k = b'_pid'
      v = b'15080'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7e1ab26d78f0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x7e1ab26d6b60>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7e1ab26d6b60>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250723_180002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-23 18:00:07.379689
Config  : ./mysite/private/backups/20250723_180002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250723_180002-mysite-database.sql.gz         1.5MiB
Backup for Site mysite has been successfully completed
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x74c610dc9080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x74c61128e3c0>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x74c612cf9180>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x74c612cf9180>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x74c612cf90d0>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x74c612cf9180>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x74c612cf90d0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x74c610f99df0>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x74c612cf9010>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x74c60fc2d9e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x74c60fc2da80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x74c610f99df0>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x74c610f99df0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x74c610f99df0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x74c610f99df0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x74c610f99df0>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x74c60fd3a8a0>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x74c60fd35580>, <class 'int'>: <function escape_int at 0x74c60fd35620>, <class 'float'>: <function escape_float at 0x74c60fd356c0>, <class 'str'>: <function escape_str at 0x74c60fd35940>, <class 'bytes'>: <function escape_bytes at 0x74c60fd358a0>, <class 'tuple'>: <function escape_sequence at 0x74c60fd35440>, <class 'list'>: <function escape_sequence at 0x74c60fd35440>, <class 'set'>: <function escape_sequence at 0x74c60fd35440>, <class 'frozenset'>: <function escape_sequence at 0x74c60fd35440>, <class 'dict'>: <function escape_dict at 0x74c60fd353a0>, <class 'NoneType'>: <function escape_None at 0x74c60fd359e0>, <class 'datetime.date'>: <function escape_date at 0x74c60fd35c60>, <class 'datetime.datetime'>: <function escape_datetime at 0x74c60fd35bc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x74c60fd35a80>, <class 'datetime.time'>: <function escape_time at 0x74c60fd35b20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x74c60fd3a8a0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x74c60fd3a8a0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14\xc3N2\xb2"\x8a\xb1\x01\xe9\xdf\x8b\x04s\xca\xb8>}^\xdf^_7574cff83173b5ba\x00mysql_native_password\x006\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0521385'
      authresp = b'\xc3N2\xb2"\x8a\xb1\x01\xe9\xdf\x8b\x04s\xca\xb8>}^\xdf^'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0521385'
      k = b'_pid'
      v = b'21385'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x74c60fd3a8a0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x74c612cf9030>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x74c612cf9030>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250724_120002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-24 12:00:07.631936
Config  : ./mysite/private/backups/20250724_120002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250724_120002-mysite-database.sql.gz         1.5MiB
Backup for Site mysite has been successfully completed
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7900a09fd080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x7900a0ece2a0>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7900a2959540>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7900a2959540>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7900a2959430>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7900a2959540>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7900a2959430>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x79009f9f77d0>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7900a2959370>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x79009f8019e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x79009f801a80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x79009f9f77d0>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x79009f9f77d0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x79009f9f77d0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x79009f9f77d0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x79009f9f77d0>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7900a0d72000>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x79009f909580>, <class 'int'>: <function escape_int at 0x79009f909620>, <class 'float'>: <function escape_float at 0x79009f9096c0>, <class 'str'>: <function escape_str at 0x79009f909940>, <class 'bytes'>: <function escape_bytes at 0x79009f9098a0>, <class 'tuple'>: <function escape_sequence at 0x79009f909440>, <class 'list'>: <function escape_sequence at 0x79009f909440>, <class 'set'>: <function escape_sequence at 0x79009f909440>, <class 'frozenset'>: <function escape_sequence at 0x79009f909440>, <class 'dict'>: <function escape_dict at 0x79009f9093a0>, <class 'NoneType'>: <function escape_None at 0x79009f9099e0>, <class 'datetime.date'>: <function escape_date at 0x79009f909c60>, <class 'datetime.datetime'>: <function escape_datetime at 0x79009f909bc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x79009f909a80>, <class 'datetime.time'>: <function escape_time at 0x79009f909b20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7900a0d72000>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7900a0d72000>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14\x1ds\x9e\x89\xc2\x9e\x0c\n\xb8\xa1\xd1\xbfP\xa1\xd6\x94tI\xdf~_7574cff83173b5ba\x00mysql_native_password\x006\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0513969'
      authresp = b'\x1ds\x9e\x89\xc2\x9e\x0c\n\xb8\xa1\xd1\xbfP\xa1\xd6\x94tI\xdf~'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0513969'
      k = b'_pid'
      v = b'13969'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7900a0d72000>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x7900a14bbc70>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7900a14bbc70>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250724_180003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-24 18:00:09.095762
Config  : ./mysite/private/backups/20250724_180003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250724_180003-mysite-database.sql.gz         1.5MiB
Backup for Site mysite has been successfully completed
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7a19e43cd080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x7a19e48923f0>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7a19e62fce20>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7a19e62fce20>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7a19e4f0c590>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7a19e62fce20>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7a19e4f0c590>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7a19e62fd220>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7a19e3223530>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7a19e32319e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7a19e3231a80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7a19e62fd220>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7a19e62fd220>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7a19e62fd220>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7a19e62fd220>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7a19e62fd220>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7a19e32233e0>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7a19e3339580>, <class 'int'>: <function escape_int at 0x7a19e3339620>, <class 'float'>: <function escape_float at 0x7a19e33396c0>, <class 'str'>: <function escape_str at 0x7a19e3339940>, <class 'bytes'>: <function escape_bytes at 0x7a19e33398a0>, <class 'tuple'>: <function escape_sequence at 0x7a19e3339440>, <class 'list'>: <function escape_sequence at 0x7a19e3339440>, <class 'set'>: <function escape_sequence at 0x7a19e3339440>, <class 'frozenset'>: <function escape_sequence at 0x7a19e3339440>, <class 'dict'>: <function escape_dict at 0x7a19e33393a0>, <class 'NoneType'>: <function escape_None at 0x7a19e33399e0>, <class 'datetime.date'>: <function escape_date at 0x7a19e3339c60>, <class 'datetime.datetime'>: <function escape_datetime at 0x7a19e3339bc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7a19e3339a80>, <class 'datetime.time'>: <function escape_time at 0x7a19e3339b20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7a19e32233e0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7a19e32233e0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b"\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14e\x83\x07b\xa7\xa4o\xb8\xf9\xe5\xdf\x19\xe9\xf3'\xe9H\xedX\x9d_7574cff83173b5ba\x00mysql_native_password\x006\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0530937"
      authresp = b"e\x83\x07b\xa7\xa4o\xb8\xf9\xe5\xdf\x19\xe9\xf3'\xe9H\xedX\x9d"
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0530937'
      k = b'_pid'
      v = b'30937'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7a19e32233e0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x7a19e32231c0>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7a19e32231c0>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250725_120003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-25 12:00:07.061785
Config  : ./mysite/private/backups/20250725_120003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250725_120003-mysite-database.sql.gz         1.5MiB
Backup for Site mysite has been successfully completed
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7404d971d080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x7404da18a480>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7404dbbf5000>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7404dbbf5000>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7404dbbf4fb0>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7404dbbf5000>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7404dbbf4fb0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7404d9790bf0>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7404dbbf4ef0>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7404d8b119e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7404d8b11a80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7404d9790bf0>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7404d9790bf0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7404d9790bf0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7404d9790bf0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7404d9790bf0>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7404d9ff8170>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7404d9619580>, <class 'int'>: <function escape_int at 0x7404d9619620>, <class 'float'>: <function escape_float at 0x7404d96196c0>, <class 'str'>: <function escape_str at 0x7404d9619940>, <class 'bytes'>: <function escape_bytes at 0x7404d96198a0>, <class 'tuple'>: <function escape_sequence at 0x7404d9619440>, <class 'list'>: <function escape_sequence at 0x7404d9619440>, <class 'set'>: <function escape_sequence at 0x7404d9619440>, <class 'frozenset'>: <function escape_sequence at 0x7404d9619440>, <class 'dict'>: <function escape_dict at 0x7404d96193a0>, <class 'NoneType'>: <function escape_None at 0x7404d96199e0>, <class 'datetime.date'>: <function escape_date at 0x7404d9619c60>, <class 'datetime.datetime'>: <function escape_datetime at 0x7404d9619bc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7404d9619a80>, <class 'datetime.time'>: <function escape_time at 0x7404d9619b20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7404d9ff8170>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7404d9ff8170>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x145~\x1bk\x15\rY\\@\xdf\x85v\xa5\x97\xbb\x1cj(\x9b\x8e_7574cff83173b5ba\x00mysql_native_password\x006\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0518538'
      authresp = b'5~\x1bk\x15\rY\\@\xdf\x85v\xa5\x97\xbb\x1cj(\x9b\x8e'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0518538'
      k = b'_pid'
      v = b'18538'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7404d9ff8170>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x7404dbbf4e50>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7404dbbf4e50>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250725_180003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-25 18:00:08.974748
Config  : ./mysite/private/backups/20250725_180003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250725_180003-mysite-database.sql.gz         1.5MiB
Backup for Site mysite has been successfully completed
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7298e050d080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x7298e0fca690>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7298e0453bb0>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7298e0453bb0>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7298e2a89250>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7298e0453bb0>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7298e2a89250>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7298e051a570>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7298e2a89130>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7298df9019e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7298df901a80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7298e051a570>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7298e051a570>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7298e051a570>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7298e051a570>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7298e051a570>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7298e1438170>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7298e0409580>, <class 'int'>: <function escape_int at 0x7298e0409620>, <class 'float'>: <function escape_float at 0x7298e04096c0>, <class 'str'>: <function escape_str at 0x7298e0409940>, <class 'bytes'>: <function escape_bytes at 0x7298e04098a0>, <class 'tuple'>: <function escape_sequence at 0x7298e0409440>, <class 'list'>: <function escape_sequence at 0x7298e0409440>, <class 'set'>: <function escape_sequence at 0x7298e0409440>, <class 'frozenset'>: <function escape_sequence at 0x7298e0409440>, <class 'dict'>: <function escape_dict at 0x7298e04093a0>, <class 'NoneType'>: <function escape_None at 0x7298e04099e0>, <class 'datetime.date'>: <function escape_date at 0x7298e0409c60>, <class 'datetime.datetime'>: <function escape_datetime at 0x7298e0409bc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7298e0409a80>, <class 'datetime.time'>: <function escape_time at 0x7298e0409b20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7298e1438170>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7298e1438170>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14\x0fqG\xac\xd3\x14:\x80\x1f5\x85\xa65\xb1?\x0c\xb6\xa7\x1e\x8c_7574cff83173b5ba\x00mysql_native_password\x006\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0567867'
      authresp = b'\x0fqG\xac\xd3\x14:\x80\x1f5\x85\xa65\xb1?\x0c\xb6\xa7\x1e\x8c'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0567867'
      k = b'_pid'
      v = b'67867'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7298e1438170>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x7298e2a89060>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7298e2a89060>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250727_180002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-27 18:00:05.591145
Config  : ./mysite/private/backups/20250727_180002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250727_180002-mysite-database.sql.gz         1.5MiB
Backup for Site mysite has been successfully completed
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7c7199f11080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x7c719a97e2d0>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c7199efba90>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c7199efba90>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7c7199efb590>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c7199efba90>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7c7199efb590>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7c719c3e8ad0>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7c7199efb8f0>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7c71993059e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7c7199305a80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7c719c3e8ad0>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7c719c3e8ad0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7c719c3e8ad0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7c719c3e8ad0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7c719c3e8ad0>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7c7199efb7a0>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7c7199e0d580>, <class 'int'>: <function escape_int at 0x7c7199e0d620>, <class 'float'>: <function escape_float at 0x7c7199e0d6c0>, <class 'str'>: <function escape_str at 0x7c7199e0d940>, <class 'bytes'>: <function escape_bytes at 0x7c7199e0d8a0>, <class 'tuple'>: <function escape_sequence at 0x7c7199e0d440>, <class 'list'>: <function escape_sequence at 0x7c7199e0d440>, <class 'set'>: <function escape_sequence at 0x7c7199e0d440>, <class 'frozenset'>: <function escape_sequence at 0x7c7199e0d440>, <class 'dict'>: <function escape_dict at 0x7c7199e0d3a0>, <class 'NoneType'>: <function escape_None at 0x7c7199e0d9e0>, <class 'datetime.date'>: <function escape_date at 0x7c7199e0dc60>, <class 'datetime.datetime'>: <function escape_datetime at 0x7c7199e0dbc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7c7199e0da80>, <class 'datetime.time'>: <function escape_time at 0x7c7199e0db20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7c7199efb7a0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7c7199efb7a0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14\xca\x8f\xf6\x10@\xf7\x93W\xefm\x1c\xcd:)\xa5\xf5\xcf+\xfc\xbc_7574cff83173b5ba\x00mysql_native_password\x006\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0512351'
      authresp = b'\xca\x8f\xf6\x10@\xf7\x93W\xefm\x1c\xcd:)\xa5\xf5\xcf+\xfc\xbc'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0512351'
      k = b'_pid'
      v = b'12351'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7c7199efb7a0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x7c71993c3400>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7c71993c3400>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250728_120002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-28 12:00:05.966553
Config  : ./mysite/private/backups/20250728_120002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250728_120002-mysite-database.sql.gz         1.5MiB
Backup for Site mysite has been successfully completed
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7d56169cd080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x7d5616e92180>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7d56188fd2d0>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7d56188fd2d0>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7d56188fd1f0>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7d56188fd2d0>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7d56188fd1f0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7d5615827500>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7d56188fd160>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7d56158319e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7d5615831a80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7d5615827500>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7d5615827500>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7d5615827500>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7d5615827500>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7d5615827500>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7d56159a62d0>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7d5615939580>, <class 'int'>: <function escape_int at 0x7d5615939620>, <class 'float'>: <function escape_float at 0x7d56159396c0>, <class 'str'>: <function escape_str at 0x7d5615939940>, <class 'bytes'>: <function escape_bytes at 0x7d56159398a0>, <class 'tuple'>: <function escape_sequence at 0x7d5615939440>, <class 'list'>: <function escape_sequence at 0x7d5615939440>, <class 'set'>: <function escape_sequence at 0x7d5615939440>, <class 'frozenset'>: <function escape_sequence at 0x7d5615939440>, <class 'dict'>: <function escape_dict at 0x7d56159393a0>, <class 'NoneType'>: <function escape_None at 0x7d56159399e0>, <class 'datetime.date'>: <function escape_date at 0x7d5615939c60>, <class 'datetime.datetime'>: <function escape_datetime at 0x7d5615939bc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7d5615939a80>, <class 'datetime.time'>: <function escape_time at 0x7d5615939b20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7d56159a62d0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7d56159a62d0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14jXA\\z\xb6\x07\x07v\xdb\xb3\r\xa7y\x8f\xd9\xd1\x8b\xb1+_7574cff83173b5ba\x00mysql_native_password\x006\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0542871'
      authresp = b'jXA\\z\xb6\x07\x07v\xdb\xb3\r\xa7y\x8f\xd9\xd1\x8b\xb1+'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0542871'
      k = b'_pid'
      v = b'42871'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7d56159a62d0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x7d56188fcfa0>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7d56188fcfa0>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250728_180003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-28 18:00:08.544500
Config  : ./mysite/private/backups/20250728_180003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250728_180003-mysite-database.sql.gz         1.6MiB
Backup for Site mysite has been successfully completed
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x72957fc41080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x7295800de3f0>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x72957ea03730>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x72957ea03730>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x72957ea039e0>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x72957ea03730>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x72957ea039e0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x729581b8d310>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x72957ea03b30>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x72957ea0d9e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x72957ea0da80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x729581b8d310>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x729581b8d310>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x729581b8d310>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x729581b8d310>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x729581b8d310>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x72957ea01a30>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x72957eb15580>, <class 'int'>: <function escape_int at 0x72957eb15620>, <class 'float'>: <function escape_float at 0x72957eb156c0>, <class 'str'>: <function escape_str at 0x72957eb15940>, <class 'bytes'>: <function escape_bytes at 0x72957eb158a0>, <class 'tuple'>: <function escape_sequence at 0x72957eb15440>, <class 'list'>: <function escape_sequence at 0x72957eb15440>, <class 'set'>: <function escape_sequence at 0x72957eb15440>, <class 'frozenset'>: <function escape_sequence at 0x72957eb15440>, <class 'dict'>: <function escape_dict at 0x72957eb153a0>, <class 'NoneType'>: <function escape_None at 0x72957eb159e0>, <class 'datetime.date'>: <function escape_date at 0x72957eb15c60>, <class 'datetime.datetime'>: <function escape_datetime at 0x72957eb15bc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x72957eb15a80>, <class 'datetime.time'>: <function escape_time at 0x72957eb15b20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x72957ea01a30>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x72957ea01a30>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14\xb6{\x891{\xa8\x0c\xb3\xe8\xa2(\xf5\xcaU\xde\xf9\xfa\x8cn\x12_7574cff83173b5ba\x00mysql_native_password\x006\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0595501'
      authresp = b'\xb6{\x891{\xa8\x0c\xb3\xe8\xa2(\xf5\xcaU\xde\xf9\xfa\x8cn\x12'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0595501'
      k = b'_pid'
      v = b'95501'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x72957ea01a30>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x72957ea03af0>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x72957ea03af0>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250729_120002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-29 12:00:03.554810
Config  : ./mysite/private/backups/20250729_120002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250729_120002-mysite-database.sql.gz         1.6MiB
Backup for Site mysite has been successfully completed
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7eb744c09080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x7eb7450d2720>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7eb746b8d360>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7eb746b8d360>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7eb746b8d310>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7eb746b8d360>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7eb746b8d310>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7eb744c14b60>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7eb74570d160>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7eb743a0d9e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7eb743a0da80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7eb744c14b60>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7eb744c14b60>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7eb744c14b60>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7eb744c14b60>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7eb744c14b60>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7eb743b1a750>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7eb743b15580>, <class 'int'>: <function escape_int at 0x7eb743b15620>, <class 'float'>: <function escape_float at 0x7eb743b156c0>, <class 'str'>: <function escape_str at 0x7eb743b15940>, <class 'bytes'>: <function escape_bytes at 0x7eb743b158a0>, <class 'tuple'>: <function escape_sequence at 0x7eb743b15440>, <class 'list'>: <function escape_sequence at 0x7eb743b15440>, <class 'set'>: <function escape_sequence at 0x7eb743b15440>, <class 'frozenset'>: <function escape_sequence at 0x7eb743b15440>, <class 'dict'>: <function escape_dict at 0x7eb743b153a0>, <class 'NoneType'>: <function escape_None at 0x7eb743b159e0>, <class 'datetime.date'>: <function escape_date at 0x7eb743b15c60>, <class 'datetime.datetime'>: <function escape_datetime at 0x7eb743b15bc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7eb743b15a80>, <class 'datetime.time'>: <function escape_time at 0x7eb743b15b20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7eb743b1a750>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7eb743b1a750>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14\x07\xec \n?\xed\x9c\x8c&a,\x14!\xe6\x97\x98\xa1t\x98\xb7_7574cff83173b5ba\x00mysql_native_password\x006\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0552504'
      authresp = b'\x07\xec \n?\xed\x9c\x8c&a,\x14!\xe6\x97\x98\xa1t\x98\xb7'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0552504'
      k = b'_pid'
      v = b'52504'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7eb743b1a750>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x7eb743a1e170>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7eb743a1e170>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
