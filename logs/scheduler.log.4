2025-07-28 16:18:29,421 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:19:29,950 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:20:29,957 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:21:30,307 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:22:30,571 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:23:31,057 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:24:31,349 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:25:31,600 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:26:31,608 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:27:31,959 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:28:32,513 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:29:32,520 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:30:32,785 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:31:33,136 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:32:33,396 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:33:33,709 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:34:34,215 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:35:34,472 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:36:34,479 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:37:35,050 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:38:35,057 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:39:35,549 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:40:35,845 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:41:36,097 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:42:36,355 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:43:36,361 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:44:36,950 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:45:37,308 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:46:37,571 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:47:37,827 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:48:37,834 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
2025-07-28 16:49:38,146 ERROR scheduler Exception in Enqueue Events for Site test-site
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 836, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
